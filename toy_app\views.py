from django.contrib.auth import authenticate
from django.core.files.storage import default_storage
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets, status, generics, filters
from rest_framework.generics import CreateAPIView, GenericAPIView, RetrieveUpdateAPIView
from rest_framework.parsers import MultiPartParser
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.permissions import IsAuthenticated, IsAdminUser

from .models import *
from .permissions import *
from .serializers import *
from .utils import send_email_verification_code


# Create your views here.


# Токен с пользователем
class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer


# Подробная информация о пользователе
class UserProfile(RetrieveUpdateAPIView):
    serializer_class = UserSelfUpdateSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user



class ChangePasswordView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        serializer = ChangePasswordSerializer(data=request.data)

        if serializer.is_valid():
            old_password = serializer.validated_data['old_password']
            new_password = serializer.validated_data['new_password']

            if not user.check_password(old_password):
                return Response({"error": "Неверный текущий пароль."}, status=status.HTTP_400_BAD_REQUEST)

            user.set_password(new_password)
            user.save()
            return Response({"detail": "Пароль успешно изменён."}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




# RegisterView
class RegisterView(CreateAPIView):
    serializer_class = RegisterSerializer


# Для получения верификации кода
class VerifyCodeView(GenericAPIView):
    serializer_class = VerifyCodeSerializer

    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response({'detail': 'Почта подтверждена. Аккаунт активирован!'})


class ResendCodeView(APIView):
    def post(self, request):
        serializer = ResendCodeSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.context['user']

            # удалим старые коды и отправим новый
            EmailVerificationCode.objects.filter(user=user).delete()
            send_email_verification_code(user)

            return Response({"detail": "Verification code resent"}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


"""
    Verification code list for admin
"""
class EmailVerificationCodeViewSet(viewsets.ModelViewSet):
    queryset = EmailVerificationCode.objects.all().select_related('user')
    serializer_class = EmailVerificationCodeSerializer
    permission_classes = [IsAdminUser]  # доступ только для админа



class UserList(generics.ListAPIView):
    queryset = User.objects.all()
    serializer_class = UserDetailSerializer
    permission_classes = [IsStaffOrReadOnly]

    # ✅ Подключаем фильтрацию и поиск
    filter_backends = [
        DjangoFilterBackend,
        filters.OrderingFilter,
        filters.SearchFilter,
    ]

    # ✅ Разрешаем фильтровать по этим полям (через query-параметры)
    filterset_fields = ['is_active', 'is_staff', 'email', 'phone']

    # ✅ Разрешаем искать по этим полям
    search_fields = ['email', 'phone']

    # ✅ Сортировка по этим полям
    ordering_fields = ['id', 'date_joined', 'last_login', 'email']
    ordering = ['-date_joined']  # по умолчанию



"""
    Изменения пользователя
"""
class AdminUserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = AdminUserUpdateSerializer
    permission_classes = [IsAdminUser]

    def destroy(self, request, *args, **kwargs):
        user = self.get_object()
        if user.is_superuser:
            return Response(
                {"error": "Нельзя удалить суперпользователя."},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().destroy(request, *args, **kwargs)




class GameViewSet(viewsets.ModelViewSet):
    queryset = Game.objects.all().order_by('-created_at')
    serializer_class = GameSerializer
    permission_classes = [IsStaffOrReadOnly]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request  # 👈 добавили request в контекст
        return context


# CART
class CartItemViewSet(viewsets.ModelViewSet):
    serializer_class = CartItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CartItem.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def create(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except serializers.ValidationError as e:
            return Response({'detail': e.detail}, status=400)


# Просмотр игр пользователя
class UserLibraryListView(generics.ListAPIView):
    serializer_class = UserLibrarySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserLibrary.objects.filter(user=self.request.user).select_related('game')


# ручное добавление
class AddToLibraryView(generics.CreateAPIView):
    serializer_class = AddToLibrarySerializer
    permission_classes = [IsAdminUser]


class GameKeyViewSet(viewsets.ModelViewSet):
    queryset = GameKey.objects.all().select_related('game', 'assigned_to_user')
    serializer_class = GameKeySerializer
    permission_classes = [IsAdminOrOwner]

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return GameKey.objects.all()
        return GameKey.objects.filter(assigned_to_user=user)


"""
    Проверка на покупку из корзины <UNK> <UNK> <UNK>
"""
class CheckoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        cart_items = CartItem.objects.filter(user=user)

        if not cart_items.exists():
            return Response({'detail': 'Корзина пуста.'}, status=status.HTTP_400_BAD_REQUEST)

        purchases = []
        skipped = []
        now = timezone.now()

        for item in cart_items:
            # Только игры в корзине (пакеты покупаются отдельно)
            game = item.game

            access_active = UserGameAccess.objects.filter(
                user=user,
                game=game,
                access_start__lte=now,
                access_end__gte=now,
                activated=True
            ).exists()

            if access_active:
                skipped.append({
                    'type': 'game',
                    'id': game.id,
                    'title': game.title,
                    'reason': 'Уже есть активный доступ'
                })
                continue

            purchase = Purchase.objects.create(
                user=user,
                game=game,
                purchase_type='game',
                price=game.price,
                status='pending'
            )
            purchases.append(purchase)

        # Очистить корзину
        cart_items.delete()

        if not purchases:
            return Response({
                'detail': 'Нет новых покупок (все игры уже активны).',
                'skipped': skipped
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = PurchaseSerializer(purchases, many=True)
        return Response({
            'purchases': serializer.data,
            'skipped': skipped
        }, status=status.HTTP_201_CREATED)


class PurchaseViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = PurchaseSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Purchase.objects.filter(user=self.request.user).order_by('-created_at')


"""
    Покупка фейковая
"""
class PurchasePaymentView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        try:
            original_purchase = Purchase.objects.get(pk=pk, user=request.user)
        except Purchase.DoesNotExist:
            return Response({'error': 'Покупка не найдена.'}, status=404)

        access_type = request.data.get('access_type')
        if access_type not in ['oneday', 'subscription']:
            return Response({'error': 'Неверный тип доступа.'}, status=400)

        now = timezone.now()

        # Проверка: если покупка оплачена — доступ ещё активен?
        if original_purchase.status == 'paid':
            access_active = UserGameAccess.objects.filter(
                user=request.user,
                game=original_purchase.game,
                access_start__lte=now,
                access_end__gte=now
            ).exists()

            if access_active:
                return Response({'detail': 'Доступ к игре уже активен.'}, status=400)

            # 👉 доступ истёк, создаём новую покупку
            new_purchase = Purchase.objects.create(
                user=request.user,
                game=original_purchase.game,
                purchase_type='renewal',
                price=original_purchase.price,
                status='pending',
            )

            purchase = new_purchase
        else:
            # обычная неоплаченная покупка
            purchase = original_purchase

        # 💳 Создаём фейковый платёж
        Payment.objects.create(
            user=request.user,
            purchase=purchase,
            amount=purchase.price,
            status='success',
            provider='other',
        )

        if access_type == 'subscription':
            access_start = now
            access_end = now + timedelta(days=30)
            activated = True
        else:  # oneday
            activated = False
            access_start = None
            access_end = None

        UserGameAccess.objects.create(
            user=request.user,
            game=purchase.game,
            access_type=access_type,
            access_start=access_start,
            access_end=access_end,
            activated=activated
        )

        # 🎮 Добавляем в библиотеку только если доступ активирован
        if activated:
            UserLibrary.objects.get_or_create(
                user=request.user,
                game=purchase.game,
                defaults={'access_source': 'individual'}
            )

        # ✅ Обновляем статус покупки
        purchase.status = 'paid'
        purchase.save()

        return Response({
            'detail': 'Доступ создан (активный — если subscription).',
            'purchase_id': purchase.id
        }, status=200)



"""
    Ручная активация для oneday
"""

class ActivateAccessView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        game_id = request.data.get('game_id')
        access_start_str = request.data.get('access_start')

        # Проверка наличия необходимых полей
        if not game_id:
            return Response({'error': 'Поле game_id обязательно.'}, status=status.HTTP_400_BAD_REQUEST)
        if not access_start_str:
            return Response({'error': 'Поле access_start обязательно.'}, status=status.HTTP_400_BAD_REQUEST)

        # Парсинг времени старта
        try:
            access_start = timezone.datetime.fromisoformat(access_start_str)
            if timezone.is_naive(access_start):
                access_start = timezone.make_aware(access_start, timezone.get_current_timezone())
        except ValueError:
            return Response({'error': 'Неверный формат даты. Используй ISO 8601 (например: 2025-07-15T19:30:00)'}, status=status.HTTP_400_BAD_REQUEST)

        # Поиск подходящего доступа
        try:
            access = UserGameAccess.objects.get(
                user=request.user,
                game_id=game_id,
                access_type='oneday',
                activated=False
            )
        except UserGameAccess.DoesNotExist:
            return Response({'error': 'Не найден неактивированный доступ.'}, status=status.HTTP_404_NOT_FOUND)

        # Активация доступа
        access.access_start = access_start
        access.access_end = access_start + timedelta(days=1)
        access.activated = True
        access.save()

        # Добавляем в библиотеку при активации
        UserLibrary.objects.get_or_create(
            user=request.user,
            game_id=game_id,
            defaults={'access_source': 'individual'}
        )

        return Response({
            'detail': 'Однодневный доступ активирован.',
            'access_start': access.access_start.isoformat(),
            'access_end': access.access_end.isoformat()
        }, status=status.HTTP_200_OK)



"""
    Просмотр статистки библиотеки и корзины
"""

class UserSummaryView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user

        cart_count = user.cart_items.count()
        library_count = user.library.count()
        pending_payments_count = Purchase.objects.filter(user=user, status='pending').count()

        return Response({
            'cart_count': cart_count,
            'library_count': library_count,
            'pending_payments_count': pending_payments_count,
        })


class UploadGalleryImageView(APIView):
    parser_classes = [MultiPartParser]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        file = request.FILES.get('file')
        if not file:
            return Response({"error": "Файл не найден"}, status=status.HTTP_400_BAD_REQUEST)

        path = default_storage.save(f'game_gallery/{file.name}', file)
        url = default_storage.url(path)

        return Response({"url": url}, status=status.HTTP_201_CREATED)


class GameGalleryItemViewSet(viewsets.ModelViewSet):
    queryset = GameGalleryItem.objects.all()
    serializer_class = GameGalleryItemSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save()


class UserGameAccessListView(generics.ListCreateAPIView):
    serializer_class = UserGameAccessSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserGameAccess.objects.filter(user=self.request.user)


"""
    Админ просматривает доступы
"""

class AdminUserGameAccessViewSet(viewsets.ModelViewSet):
    queryset = UserGameAccess.objects.all().select_related('user', 'game')
    serializer_class = AdminUserGameAccess
    permission_classes = [IsAdminUser]


class GameAccessAuthView(APIView):
    def post(self, request):
        user_code = request.data.get('user_code')
        game_code = request.data.get('game_code')

        # 🔐 Проверка параметров
        if not user_code or not game_code:
            return Response(
                {'error': 'user_code и game_code обязательны.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 🔎 Найти пользователя
        try:
            user = User.objects.get(user_code=user_code)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден.'}, status=status.HTTP_404_NOT_FOUND)

        # 🔎 Найти игру по уникальному коду
        try:
            game = Game.objects.get(game_code=game_code)
        except Game.DoesNotExist:
            return Response({'error': 'Игра не найдена.'}, status=status.HTTP_404_NOT_FOUND)

        # ⏳ Проверить доступ по времени
        now = timezone.now()
        access = UserGameAccess.objects.filter(
            user=user,
            game=game,
            access_start__lte=now,
            access_end__gte=now
        ).order_by('-access_end').first()

        return Response({
            'has_access': access is not None,
            'access_end': access.access_end if access else None
        }, status=200)





"""
    Пакет игр список вьюшка
"""
class GamePackageViewSet(viewsets.ModelViewSet):
    queryset = GamePackage.objects.all()
    serializer_class = GamePackageSerializer
    permission_classes = [IsStaffOrReadOnly]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class PurchasePackageView(APIView):
    """
    Прямая покупка пакета (не через корзину)
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        package_id = request.data.get('package_id')

        if not package_id:
            return Response(
                {'error': 'package_id обязателен.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            package = GamePackage.objects.get(id=package_id)
        except GamePackage.DoesNotExist:
            return Response(
                {'error': 'Пакет не найден.'},
                status=status.HTTP_404_NOT_FOUND
            )

        user = request.user

        # Проверяем, нет ли уже активной подписки на этот пакет
        existing_subscription = UserPackageSubscription.objects.filter(
            user=user,
            package=package,
            is_active=True,
            expires_at__gt=timezone.now()
        ).first()

        if existing_subscription:
            return Response(
                {'error': 'У вас уже есть активная подписка на этот пакет.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Создаём покупку
        purchase = Purchase.objects.create(
            user=user,
            package=package,
            purchase_type='package',
            price=package.price,
            status='pending'
        )

        # Фейковая оплата (в реальности здесь будет интеграция с платёжной системой)
        Payment.objects.create(
            user=user,
            purchase=purchase,
            amount=package.price,
            status='success',
            provider='other',
        )

        # Создаём подписку на пакет
        expires_at = timezone.now() + timedelta(days=package.duration_days)
        subscription = UserPackageSubscription.objects.create(
            user=user,
            package=package,
            purchase=purchase,
            expires_at=expires_at
        )

        # Обновляем статус покупки
        purchase.status = 'paid'
        purchase.save()

        return Response({
            'detail': 'Пакет успешно приобретён.',
            'subscription_id': subscription.id,
            'expires_at': expires_at.isoformat(),
            'max_selectable_games': package.max_selectable_games
        }, status=status.HTTP_201_CREATED)


class SelectPackageGamesView(APIView):
    """
    Выбор игр из купленного пакета
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        subscription_id = request.data.get('subscription_id')
        game_ids = request.data.get('game_ids', [])

        if not subscription_id or not game_ids:
            return Response(
                {'error': 'subscription_id и game_ids обязательны.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            subscription = UserPackageSubscription.objects.get(
                id=subscription_id,
                user=request.user,
                is_active=True
            )
        except UserPackageSubscription.DoesNotExist:
            return Response(
                {'error': 'Подписка на пакет не найдена.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Проверяем, не истекла ли подписка
        if subscription.is_expired():
            return Response(
                {'error': 'Подписка на пакет истекла.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Проверяем количество игр
        total_games_to_select = len(game_ids)
        if subscription.games_selected_count + total_games_to_select > subscription.package.max_selectable_games:
            return Response(
                {'error': f'Вы можете выбрать максимум {subscription.package.max_selectable_games} игр. '
                          f'Уже выбрано: {subscription.games_selected_count}'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Проверяем, что все игры принадлежат пакету
        package_game_ids = list(subscription.package.games.values_list('id', flat=True))
        invalid_games = [gid for gid in game_ids if gid not in package_game_ids]
        if invalid_games:
            return Response(
                {'error': f'Игры с ID {invalid_games} не входят в этот пакет.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Проверяем, что игры ещё не выбраны
        already_selected = subscription.selected_games.filter(id__in=game_ids).values_list('id', flat=True)
        if already_selected:
            return Response(
                {'error': f'Игры с ID {list(already_selected)} уже выбраны.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Добавляем игры к подписке
        games_to_add = Game.objects.filter(id__in=game_ids)
        subscription.selected_games.add(*games_to_add)
        subscription.games_selected_count += total_games_to_select
        subscription.save()

        # Создаём доступ к играм и добавляем в библиотеку
        for game in games_to_add:
            # Создаём доступ через пакет
            UserGameAccess.objects.create(
                user=request.user,
                game=game,
                access_type='package',
                access_start=timezone.now(),
                access_end=subscription.expires_at,
                activated=True,
                package_subscription=subscription
            )

            # Добавляем в библиотеку
            UserLibrary.objects.get_or_create(
                user=request.user,
                game=game,
                defaults={
                    'access_source': 'package',
                    'package_subscription': subscription
                }
            )

        return Response({
            'detail': f'Выбрано игр: {total_games_to_select}',
            'remaining_slots': subscription.remaining_game_slots(),
            'selected_games': list(games_to_add.values('id', 'title'))
        }, status=status.HTTP_200_OK)


class UserPackageSubscriptionsView(generics.ListAPIView):
    """
    Список активных подписок пользователя на пакеты
    """
    serializer_class = UserPackageSubscriptionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserPackageSubscription.objects.filter(
            user=self.request.user,
            is_active=True
        ).select_related('package').prefetch_related('selected_games')


class PackageSubscriptionDetailView(generics.RetrieveAPIView):
    """
    Детали конкретной подписки на пакет
    """
    serializer_class = UserPackageSubscriptionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserPackageSubscription.objects.filter(
            user=self.request.user,
            is_active=True
        ).select_related('package').prefetch_related('selected_games')


class GameFileViewSet(viewsets.ModelViewSet):
    queryset = GameFile.objects.all().select_related('game')
    serializer_class = GameFileSerializer
    permission_classes = [IsStaffOrReadOnly]
    parser_classes = [MultiPartParser]  # Добавляем поддержку файлов

    def get_queryset(self):
        user = self.request.user
        queryset = GameFile.objects.select_related('game')

        # Если пользователь не админ, показываем только файлы игр, к которым у него есть доступ
        if not user.is_staff:
            if not user.is_authenticated:
                return queryset.none()

            now = timezone.now()
            accessible_games = UserGameAccess.objects.filter(
                user=user,
                access_start__lte=now,
                access_end__gte=now
            ).values_list('game_id', flat=True)

            queryset = queryset.filter(game_id__in=accessible_games, is_active=True)

        return queryset.order_by('-uploaded_at')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context





class DownloadGameFileView(APIView):
    """
    API для скачивания файлов игры с проверкой доступа
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, file_id):
        try:
            game_file = GameFile.objects.select_related('game').get(id=file_id, is_active=True)
        except GameFile.DoesNotExist:
            return Response(
                {'error': 'Файл не найден.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Проверка доступа к игре
        user = request.user
        now = timezone.now()
        has_access = UserGameAccess.objects.filter(
            user=user,
            game=game_file.game,
            access_start__lte=now,
            access_end__gte=now
        ).exists()

        if not has_access:
            return Response(
                {'error': 'У вас нет доступа к этой игре.'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Возвращаем файл для скачивания
        try:
            from django.http import FileResponse
            import os

            file_path = game_file.file.path
            if not os.path.exists(file_path):
                return Response(
                    {'error': 'Файл не найден на сервере.'},
                    status=status.HTTP_404_NOT_FOUND
                )

            response = FileResponse(
                open(file_path, 'rb'),
                as_attachment=True,
                filename=game_file.file_name
            )
            return response

        except Exception as e:
            return Response(
                {'error': f'Ошибка при скачивании файла: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )