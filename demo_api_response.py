#!/usr/bin/env python
"""
Demo script to show the API response format with has_active_subscription field
"""
import os
import django
from django.test import RequestFactory
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'DjangoBaseApp.settings')
django.setup()

from toy_app.models import GamePackage, UserPackageSubscription, Purchase
from toy_app.serializers import GamePackageSerializer
from django.utils import timezone
from datetime import timedelta
import json

User = get_user_model()

def demo_api_response():
    """Demo the API response format"""
    
    # Get or create a test user
    user, _ = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={'user_code': 'DEMO1'}
    )
    
    # Get existing packages or create a demo one
    packages = GamePackage.objects.all()
    if not packages.exists():
        package = GamePackage.objects.create(
            name='Premium Gaming Package',
            description='Access to premium games for 30 days',
            price=49.99,
            duration_days=30,
            max_selectable_games=3,
            benefit_1='Access to 3 premium games',
            benefit_2='30 days of unlimited play',
            benefit_3='Priority customer support'
        )
        packages = [package]
    
    # Create request factory
    factory = RequestFactory()
    request = factory.get('/api/game-packages/')
    request.user = user
    
    print("=== Game Packages API Response ===")
    print("URL: http://localhost:8000/api/game-packages/")
    print()
    
    # Serialize all packages
    serializer = GamePackageSerializer(packages, many=True, context={'request': request})
    response_data = serializer.data
    
    print("Response JSON:")
    print(json.dumps(response_data, indent=2, ensure_ascii=False))
    
    print("\n=== Key Features ===")
    print("✅ Added 'has_active_subscription' field to each package")
    print("✅ Shows 'false' when user has no active subscription")
    print("✅ Shows 'true' when user has an active subscription")
    print("✅ Checks subscription expiration date")
    print("✅ Works for authenticated and unauthenticated users")
    
    print("\n=== Frontend Usage ===")
    print("You can now use this field in your frontend to:")
    print("- Show 'You already have an active subscription' on the button")
    print("- Disable the purchase button if user already has subscription")
    print("- Display different UI states based on subscription status")
    
    print("\n=== Example Frontend Code ===")
    print("""
// In your React/Vue/Angular component:
gamePackages.forEach(package => {
    if (package.has_active_subscription) {
        // Show "You already have an active subscription"
        button.text = "You already have an active subscription";
        button.disabled = true;
    } else {
        // Show normal purchase button
        button.text = `Buy for $${package.price}`;
        button.disabled = false;
    }
});
    """)

if __name__ == '__main__':
    demo_api_response()
