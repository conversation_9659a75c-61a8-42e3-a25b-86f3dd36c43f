from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

from .models import GamePackage, UserPackageSubscription, Purchase, Game
from .serializers import GamePackageSerializer

User = get_user_model()


class GamePackageSubscriptionTestCase(TestCase):
    """Test cases for the has_active_subscription functionality"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()

        # Create test users
        self.user_with_subscription = User.objects.create(
            email='<EMAIL>',
            user_code='SUB01'
        )

        self.user_without_subscription = User.objects.create(
            email='<EMAIL>',
            user_code='NOSUB'
        )

        # Create test package
        self.package = GamePackage.objects.create(
            name='Test Package',
            description='Test package for subscription testing',
            price=29.99,
            duration_days=30,
            max_selectable_games=2
        )

        # Create active subscription for first user
        self.purchase = Purchase.objects.create(
            user=self.user_with_subscription,
            package=self.package,
            purchase_type='package',
            price=self.package.price,
            status='paid'
        )

        self.subscription = UserPackageSubscription.objects.create(
            user=self.user_with_subscription,
            package=self.package,
            purchase=self.purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

    def test_user_with_active_subscription(self):
        """Test that user with active subscription sees has_active_subscription=True"""
        request = self.factory.get('/api/game-packages/')
        request.user = self.user_with_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertTrue(data['has_active_subscription'])

    def test_user_without_subscription(self):
        """Test that user without subscription sees has_active_subscription=False"""
        request = self.factory.get('/api/game-packages/')
        request.user = self.user_without_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])

    def test_user_with_expired_subscription(self):
        """Test that user with expired subscription sees has_active_subscription=False"""
        # Make subscription expired
        self.subscription.expires_at = timezone.now() - timedelta(days=1)
        self.subscription.save()

        request = self.factory.get('/api/game-packages/')
        request.user = self.user_with_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])

    def test_unauthenticated_user(self):
        """Test that unauthenticated user sees has_active_subscription=False"""
        request = self.factory.get('/api/game-packages/')
        request.user = None

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])

    def test_inactive_subscription(self):
        """Test that user with inactive subscription sees has_active_subscription=False"""
        # Make subscription inactive
        self.subscription.is_active = False
        self.subscription.save()

        request = self.factory.get('/api/game-packages/')
        request.user = self.user_with_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])


class MyPackagesImageTestCase(TestCase):
    """Test cases for the available_games field including cover_image"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create(
            email='<EMAIL>',
            user_code='IMG01'
        )

        # Create test games with and without images
        self.game_with_image = Game.objects.create(
            title='Game With Image',
            game_code='GWI01',
            description='Game that has a cover image',
            price=19.99,
            cover_image='game_covers/test_image.jpg'  # Simulated image path
        )

        self.game_without_image = Game.objects.create(
            title='Game Without Image',
            game_code='GWO01',
            description='Game that has no cover image',
            price=24.99
            # cover_image is None/blank
        )

        # Create test package
        self.package = GamePackage.objects.create(
            name='Image Test Package',
            description='Package for testing image inclusion',
            price=39.99,
            duration_days=30,
            max_selectable_games=2
        )

        # Add games to package
        self.package.games.add(self.game_with_image, self.game_without_image)

        # Create subscription
        self.purchase = Purchase.objects.create(
            user=self.user,
            package=self.package,
            purchase_type='package',
            price=self.package.price,
            status='paid'
        )

        self.subscription = UserPackageSubscription.objects.create(
            user=self.user,
            package=self.package,
            purchase=self.purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

    def test_available_games_includes_cover_image(self):
        """Test that available_games field includes cover_image"""
        from .serializers import UserPackageSubscriptionSerializer

        serializer = UserPackageSubscriptionSerializer(self.subscription)
        data = serializer.data

        # Check that available_games is present
        self.assertIn('available_games', data)

        # Check that we have games available
        available_games = data['available_games']
        self.assertEqual(len(available_games), 2)  # Both games should be available

        # Check that each game has the required fields including cover_image
        for game in available_games:
            self.assertIn('id', game)
            self.assertIn('title', game)
            self.assertIn('cover_image', game)  # This is the key test

        # Find specific games and check their cover_image values
        game_with_image_data = next(g for g in available_games if g['title'] == 'Game With Image')
        game_without_image_data = next(g for g in available_games if g['title'] == 'Game Without Image')

        # Game with image should have cover_image value
        self.assertIsNotNone(game_with_image_data['cover_image'])
        self.assertIn('test_image.jpg', game_with_image_data['cover_image'])

        # Game without image should have null/empty cover_image
        self.assertIsNone(game_without_image_data['cover_image'])

    def test_selected_games_not_in_available_games(self):
        """Test that selected games are excluded from available_games"""
        from .serializers import UserPackageSubscriptionSerializer

        # Select one game
        self.subscription.selected_games.add(self.game_with_image)
        self.subscription.games_selected_count = 1
        self.subscription.save()

        serializer = UserPackageSubscriptionSerializer(self.subscription)
        data = serializer.data

        # Should only have one available game now
        available_games = data['available_games']
        self.assertEqual(len(available_games), 1)

        # The available game should be the one we didn't select
        self.assertEqual(available_games[0]['title'], 'Game Without Image')
        self.assertIn('cover_image', available_games[0])


class UserProfileSubscriptionTestCase(TestCase):
    """Test cases for active subscriptions in user profile endpoint"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create(
            email='<EMAIL>',
            user_code='PROF1'
        )

        # Create test package
        self.package = GamePackage.objects.create(
            name='Profile Test Package',
            description='Package for testing profile endpoint',
            price=29.99,
            duration_days=30,
            max_selectable_games=2
        )

        # Create test game
        self.game = Game.objects.create(
            title='Profile Test Game',
            game_code='PTG01',
            description='Game for profile testing',
            price=19.99
        )

        self.package.games.add(self.game)

    def test_userprofile_without_subscription(self):
        """Test user profile without any subscriptions"""
        from .serializers import UserSelfUpdateSerializer

        serializer = UserSelfUpdateSerializer(self.user)
        data = serializer.data

        # Check that active_subscriptions field is present
        self.assertIn('active_subscriptions', data)

        # Should be empty list when no subscriptions
        self.assertEqual(data['active_subscriptions'], [])

    def test_userprofile_with_active_subscription(self):
        """Test user profile with active subscription"""
        from .serializers import UserSelfUpdateSerializer

        # Create active subscription
        purchase = Purchase.objects.create(
            user=self.user,
            package=self.package,
            purchase_type='package',
            price=self.package.price,
            status='paid'
        )

        subscription = UserPackageSubscription.objects.create(
            user=self.user,
            package=self.package,
            purchase=purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

        serializer = UserSelfUpdateSerializer(self.user)
        data = serializer.data

        # Check that active_subscriptions field is present and has data
        self.assertIn('active_subscriptions', data)
        self.assertEqual(len(data['active_subscriptions']), 1)

        # Check subscription data structure
        subscription_data = data['active_subscriptions'][0]
        self.assertEqual(subscription_data['id'], subscription.id)
        self.assertEqual(subscription_data['package_name'], self.package.name)
        self.assertEqual(subscription_data['package_description'], self.package.description)
        self.assertIn('expires_at', subscription_data)
        self.assertIn('remaining_slots', subscription_data)
        self.assertIn('available_games', subscription_data)
        self.assertIn('selected_games', subscription_data)

    def test_userprofile_with_expired_subscription(self):
        """Test user profile with expired subscription (should not appear)"""
        from .serializers import UserSelfUpdateSerializer

        # Create expired subscription
        purchase = Purchase.objects.create(
            user=self.user,
            package=self.package,
            purchase_type='package',
            price=self.package.price,
            status='paid'
        )

        subscription = UserPackageSubscription.objects.create(
            user=self.user,
            package=self.package,
            purchase=purchase,
            expires_at=timezone.now() - timedelta(days=1),  # Expired
            is_active=True
        )

        serializer = UserSelfUpdateSerializer(self.user)
        data = serializer.data

        # Expired subscription should not appear in active_subscriptions
        self.assertIn('active_subscriptions', data)
        self.assertEqual(data['active_subscriptions'], [])
