from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

from .models import GamePackage, UserPackageSubscription, Purchase
from .serializers import GamePackageSerializer

User = get_user_model()


class GamePackageSubscriptionTestCase(TestCase):
    """Test cases for the has_active_subscription functionality"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()

        # Create test users
        self.user_with_subscription = User.objects.create(
            email='<EMAIL>',
            user_code='SUB01'
        )

        self.user_without_subscription = User.objects.create(
            email='<EMAIL>',
            user_code='NOSUB'
        )

        # Create test package
        self.package = GamePackage.objects.create(
            name='Test Package',
            description='Test package for subscription testing',
            price=29.99,
            duration_days=30,
            max_selectable_games=2
        )

        # Create active subscription for first user
        self.purchase = Purchase.objects.create(
            user=self.user_with_subscription,
            package=self.package,
            purchase_type='package',
            price=self.package.price,
            status='paid'
        )

        self.subscription = UserPackageSubscription.objects.create(
            user=self.user_with_subscription,
            package=self.package,
            purchase=self.purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

    def test_user_with_active_subscription(self):
        """Test that user with active subscription sees has_active_subscription=True"""
        request = self.factory.get('/api/game-packages/')
        request.user = self.user_with_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertTrue(data['has_active_subscription'])

    def test_user_without_subscription(self):
        """Test that user without subscription sees has_active_subscription=False"""
        request = self.factory.get('/api/game-packages/')
        request.user = self.user_without_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])

    def test_user_with_expired_subscription(self):
        """Test that user with expired subscription sees has_active_subscription=False"""
        # Make subscription expired
        self.subscription.expires_at = timezone.now() - timedelta(days=1)
        self.subscription.save()

        request = self.factory.get('/api/game-packages/')
        request.user = self.user_with_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])

    def test_unauthenticated_user(self):
        """Test that unauthenticated user sees has_active_subscription=False"""
        request = self.factory.get('/api/game-packages/')
        request.user = None

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])

    def test_inactive_subscription(self):
        """Test that user with inactive subscription sees has_active_subscription=False"""
        # Make subscription inactive
        self.subscription.is_active = False
        self.subscription.save()

        request = self.factory.get('/api/game-packages/')
        request.user = self.user_with_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])
